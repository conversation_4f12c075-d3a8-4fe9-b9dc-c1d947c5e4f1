"use client"

import { ProductSlideshow } from "@/components/product-slideshow"
import { mockProducts } from "@/lib/mock-data"
import { Product } from "@/lib/types"

export function FeaturedProducts() {
  // Get all products (only products with real images, no placeholders)
  const allProducts = mockProducts
    .filter(product => !product.images[0]?.includes('placeholder'))

  return (
    <ProductSlideshow
      products={allProducts}
      title="All Products"
      subtitle="Discover our complete collection of quality products - from electronics to fashion, beauty to food"
      autoPlayInterval={6000}
      className="bg-white"
    />
  )
}

export function NewArrivals() {
  // Get newest products (only products with real images)
  const newProducts = mockProducts
    .filter(product => !product.images[0]?.includes('placeholder'))
    .slice(-8) // Get last 8 products as "new arrivals"
    .reverse() // Show newest first

  return (
    <ProductSlideshow
      products={newProducts}
      title="New Arrivals"
      subtitle="Be the first to discover our latest collection of cutting-edge products"
      autoPlayInterval={7000}
      className="bg-gradient-to-br from-slate-50 to-white"
    />
  )
}

export function BestSellers() {
  // Get best selling products (only products with real images and high ratings)
  const bestSellers = mockProducts
    .filter(product => product.rating >= 4.0 && !product.images[0]?.includes('placeholder'))
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 8)

  return (
    <ProductSlideshow
      products={bestSellers}
      title="Best Sellers"
      subtitle="Join thousands of satisfied customers who love these top-rated products"
      autoPlayInterval={5500}
      className="bg-gradient-to-br from-blue-50 to-indigo-50"
    />
  )
}

export function SaleProducts() {
  // Get products on sale (only products with real images)
  const saleProducts = mockProducts
    .filter(product => product.salePrice && !product.images[0]?.includes('placeholder'))
    .slice(0, 8)

  if (saleProducts.length === 0) {
    return null // Don't render if no sale products
  }

  return (
    <ProductSlideshow
      products={saleProducts}
      title="Special Offers"
      subtitle="Limited time deals on premium products - save big while stocks last!"
      autoPlayInterval={4500}
      className="bg-gradient-to-br from-red-50 to-orange-50"
    />
  )
}
